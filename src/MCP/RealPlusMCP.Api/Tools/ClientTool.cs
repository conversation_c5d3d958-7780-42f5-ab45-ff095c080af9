using System.ComponentModel;
using Dapper;
using Microsoft.Data.SqlClient;
using ModelContextProtocol.Server;
using RealPlusMCP.Api.Common;

namespace RealPlusMCP.Api.Tools;

// [McpServerToolType]
public class ClientTool
{
    // [McpServerTool(Name = "get_clients"),
    //      Description("Get clients/companies list.")]
    public async Task<IReadOnlyCollection<ClientModel>> GetClients(
        IConfiguration configuration,
        ILogger<ClientTool> logger,
        [Description("Deployment type, i.e. the data source for the clients/companies list. Use DeploymentType.Resource if nothing is specified.")]
        DeploymentType deploymentType = DeploymentType.Resource)
    {
        var connectionString = deploymentType == DeploymentType.Resource
            ? configuration.GetConnectionString("ResourceEntities")
            : configuration.GetConnectionString("AanycEntities")
            ?? throw new InvalidOperationException("Connection string not found.");

        // Create database connection
        using var connection = new SqlConnection(connectionString);

        try
        {
            var sql = @"
                select c.companycode as Code,
                       c.companyname as Name
                from tbCompany c
                inner join tbCompanyPreference cp on cp.CompanyId = c.CompanyId
                where c.client = 1 and c.IsDeleted = 0
            ";
            var dbResults = await connection.QueryAsync<ClientModel>(
                sql
            );

            return dbResults.ToList().AsReadOnly();
        }
        catch (SqlException ex)
        {
            logger.LogError(ex, "Failed to get the list of the clients");

            return Array.Empty<ClientModel>().AsReadOnly();
        }
    }

    // [McpServerTool(Name = "get_agents_and_customers_activity"),
    //      Description("Get clients/companies activity statistics for the specified clients/companies.")]
    public async Task<IReadOnlyCollection<AgentsAndCustomersActivityModel>> GetAgentsAndCustomersActivity(
        IConfiguration configuration,
        ILogger<ClientTool> logger,
        [Description("The codes of the clients/companies. Can be obtained from get_clients tool.")]
        string[] clientCodes,
        [Description("Deployment type, i.e. the data source for the client/company analytics. Use DeploymentType.Resource if nothing is specified.")]
        DeploymentType deploymentType = DeploymentType.Resource)
    {
        return clientCodes.Select(c =>
            new AgentsAndCustomersActivityModel(c, "Client " + c, 10))
            .ToList().AsReadOnly();
    }
}

public sealed record ClientModel(
    [property: Description("The code of the client/company")] string Code,
    [property: Description("The name of the client/company")] string Name);

public sealed record AgentsAndCustomersActivityModel(
    [property: Description("The code of the client/company")] string Code,
    [property: Description("The name of the client/company")] string Name,
    [property: Description("The number of active listings for the client/company")] int ActiveListings);