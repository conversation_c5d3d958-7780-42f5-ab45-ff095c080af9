using System.ComponentModel;
using Dapper;
using Microsoft.Data.SqlClient;
using ModelContextProtocol.Server;
using RealPlusMCP.Api.Common;

namespace RealPlusMCP.Api.Tools;

[McpServerToolType]
public class ClientTool
{
    [McpServerTool(Name = "get_companies"),
         Description("Get clients/companies/tenants full list.")]
    public async Task<IReadOnlyCollection<CompanyModel>> GetCompanies(
        IConfiguration configuration,
        ILogger<ClientTool> logger,
        [Description("Deployment type, i.e. the data source for the clients/companies/tenants list. Use DeploymentType.Resource if nothing is specified.")]
        DeploymentType deploymentType = DeploymentType.Resource)
    {
        var connectionString = deploymentType == DeploymentType.Resource
            ? configuration.GetConnectionString("ResourceEntities")
            : configuration.GetConnectionString("AanycEntities")
            ?? throw new InvalidOperationException($"Connection string for {deploymentType} not found.");

        // Create database connection
        using var connection = new SqlConnection(connectionString);

        try
        {
            var sql = @"
                select c.companycode as Code,
                       c.companyname as Name
                from tbCompany c
                inner join tbCompanyPreference cp on cp.CompanyId = c.CompanyId
                where c.client = 1 and c.IsDeleted = 0
            ";
            var dbResults = await connection.QueryAsync<CompanyModel>(
                sql
            );

            return dbResults.ToList().AsReadOnly();
        }
        catch (SqlException ex)
        {
            logger.LogError(ex, "Failed to get the list of the clients");

            return Array.Empty<CompanyModel>().AsReadOnly();
        }
    }

    [McpServerTool(Name = "get_agents_and_customers_ytd_activity"),
         Description("Get agents and customers year-to-date activity statistics for the specified clients/companies/tenants.")]
    public async Task<IReadOnlyCollection<AgentsAndCustomersActivityModel>> GetAgentsAndCustomersYtdActivity(
        IConfiguration configuration,
        ILogger<ClientTool> logger,
        [Description("The codes of the clients/companies/tenants. Can be obtained from get_companies tool.")]
        string[] companyCodes,
        [Description("Deployment type, i.e. the data source for the clients/companies/tenants list. Use DeploymentType.Resource if nothing is specified.")]
        DeploymentType deploymentType = DeploymentType.Resource)
    {
        var connectionString = deploymentType == DeploymentType.Resource
            ? configuration.GetConnectionString("ResourceEntities")
            : configuration.GetConnectionString("AanycEntities")
            ?? throw new InvalidOperationException($"Connection string for {deploymentType} not found.");

        // Create database connection
        using var connection = new SqlConnection(connectionString);
        try
        {
            var sql = @"
                ;WITH ActiveAgentsPerCompany AS
                (
                    SELECT  tc.CompanyID,
                            COUNT(*) AS ActiveAgentsCount
                    FROM    dbo.tbCompany            AS tc
                    INNER JOIN dbo.tbOffice          AS o   ON  o.CompanyID = tc.CompanyID  AND o.IsDeleted   = 0
                    INNER JOIN dbo.tbAgentOffice     AS ao  ON  ao.OfficeID = o.OfficeID    AND ao.IsDeleted  = 0
                    INNER JOIN dbo.tbAgent           AS a   ON  a.AgentID  = ao.AgentID     AND a.IsDeleted   = 0
                    INNER JOIN dbo.tbPerson          AS p   ON  p.PersonID = a.AgentID      AND p.IsDeActivated = 0
                    GROUP BY tc.CompanyID
                )
                SELECT  c.CompanyCode       AS Code,
                        c.CompanyName       AS Name,
                        ISNULL(aa.ActiveAgentsCount,0) AS ActiveAgentsCount
                FROM    dbo.tbCompany            AS c
                INNER JOIN dbo.tbCompanyPreference AS cp ON cp.CompanyID = c.CompanyID
                LEFT  JOIN ActiveAgentsPerCompany  AS aa ON aa.CompanyID = c.CompanyID
                WHERE   c.Client      = 1
                AND   c.IsDeleted   = 0
                AND   c.CompanyCode IN @companyCodes;
            ";
            var dbResults = await connection.QueryAsync<AgentsAndCustomersActivityModel>(
                sql,
                new { companyCodes }
            );

            return dbResults.ToList().AsReadOnly();
        }
        catch (SqlException ex)
        {
            logger.LogError(ex, "Failed to get the activity statistics for the specified clients/companies/tenants.");

            return Array.Empty<AgentsAndCustomersActivityModel>().AsReadOnly();
        }

        // return companyCodes.Select(c =>
        //     new AgentsAndCustomersActivityModel(c, "Client " + c, 10))
        //     .ToList().AsReadOnly();
    }
}

public sealed record CompanyModel(
    [property: Description("The code of the client/company/tenant. Contains 4 numbers and uppercase letters in total.")] string Code,
    [property: Description("The name of the client/company/tenant")] string Name);

public sealed record AgentsAndCustomersActivityModel(
    [property: Description("The code of the client/company/tenant. Contains 4 numbers and uppercase letters in total.")] string Code,
    [property: Description("The name of the client/company/tenant")] string Name,
    [property: Description("The number of active listings for the client/company/tenant")] int ActiveAgentsCount);